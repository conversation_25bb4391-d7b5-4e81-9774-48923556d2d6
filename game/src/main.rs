use simple_wgpu_engine::*;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // Initialize the engine
    core::init()?;

    // Create a window
    window::new("Simple WGPU Game", 1)?;

    // Set engine settings
    settings::set(
        settings::VsyncType::Off,
        settings::FullscreenType::Windowed,
        settings::FpsLimit::Fps60,
        settings::MsaaType::Off,  // 4x MSAA for better quality
        1280,
        720,
    )?;

    println!("Engine initialized successfully!");
    println!("Press ESC or close window to exit");

    // Run the game with initialization and render callbacks
    core::run(init_game, render_frame)?;

    // Shutdown
    core::shutdown()?;
    println!("Engine shutdown complete");

    Ok(())
}

fn init_game() -> simple_wgpu_engine::Result<()> {
    // Load assets after window and pipeline are created
    asset::load_sprite("fc1.png", "player")?;
    asset::load_font("determinationmonoweb-webfont.ttf", "main_font")?;

    // Load quit sprites
    asset::load_sprite("quit/quit1.png", "quit1")?;
    asset::load_sprite("quit/quit2.png", "quit2")?;
    asset::load_sprite("quit/quit3.png", "quit3")?;
    asset::load_sprite("quit/quit4.png", "quit4")?;
    asset::load_sprite("quit/quit5.png", "quit5")?;

    // Set up input actions
    create_action("quit")?;
    bind_key("quit", Key::Named(NamedKey::Escape))?;

    println!("Assets loaded successfully!");
    println!("Input system configured - Hold ESC to quit");
    Ok(())
}

fn render_frame(frame_count: u32) -> simple_wgpu_engine::Result<()> {

    render::start()?;

    render::clearcolor()?;
    render::cleardepth()?;

    let delta_time = render::get_delta_time()?;
    // Handle quit sequence
    static mut QUIT_HOLD_TIME: f32 = 0.0;
    static mut QUIT_SPRITES_SHOWN: i32 = 0;

    unsafe {
        if is_action_held("quit") {
            QUIT_HOLD_TIME += delta_time;

            // Show sprites progressively every 0.5 seconds
            let sprites_to_show = (QUIT_HOLD_TIME / 0.5) as i32;
            QUIT_SPRITES_SHOWN = sprites_to_show.min(5);

            // Quit after showing all 5 sprites
            if QUIT_SPRITES_SHOWN >= 5 {
                println!("Quit sequence complete, exiting");
                std::process::exit(0);
            }
        } else {
            // Reset quit sequence when not holding
            QUIT_HOLD_TIME = 0.0;
            QUIT_SPRITES_SHOWN = 0;
        }
    }





    static mut TOTAL_TIME: f32 = 0.0;
    unsafe {
        TOTAL_TIME += delta_time;
        let rotation_speed = 1.0; // radians per second
        let rotation = TOTAL_TIME * rotation_speed;
        // Center the sprite in the new coordinate system (0,0 = top-left, 1,1 = bottom-right)
        render::sprite("player", 0.5, 0.5, 0.0, 0.2, rotation)?;
    }

    // Draw current quit sprite in top left corner (after player sprite)
    unsafe {
        if QUIT_SPRITES_SHOWN > 0 {
            let sprite_name = format!("quit{}", QUIT_SPRITES_SHOWN);
            let scale = 0.15;
            // In new coordinate system: (0,0) = top-left, (1,1) = bottom-right
            // Position sprite in top-left corner with some padding
            let x = scale * 0.5; // Half sprite width from left edge
            let y = scale * 0.5; // Half sprite height from top edge
            render::sprite(&sprite_name, x, y, 0.1, scale, 0.0)?; // No rotation
        }
    }

    // Draw moving text
    static mut TEXT_POS: f32 = 0.0;
    unsafe {
        TEXT_POS += delta_time * 0.2; // Move right by 0.2 units per second
        // Reset position when text moves off screen
        if TEXT_POS > 1.2 {
            TEXT_POS = -0.2;
        }
        // Position text in the middle vertically, moving horizontally
        render::text("Hello,. WGPU!0009", "main_font", TEXT_POS, 0.8, 0.0, 0.05, 0.0)?;
    }

    // End frame
    render::stop()?;

    // Print FPS every 60 frames
    if frame_count % 200 == 0 {
        let fps = render::get_fps()?;
        let delta = render::get_delta_time()?;
        println!("Frame {}: FPS: {:.1}, Delta: {:.4}s", frame_count, fps, delta);
    }

    Ok(())
}
