// Text rendering shader

struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) tex_coords: vec2<f32>,
}

struct TextInstance {
    @location(2) transform_0: vec4<f32>,
    @location(3) transform_1: vec4<f32>,
    @location(4) transform_2: vec4<f32>,
    @location(5) transform_3: vec4<f32>,
    @location(6) atlas_coords: vec4<f32>, // x, y, width, height in font atlas (normalized)
    @location(7) color: vec4<f32>,
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) tex_coords: vec2<f32>,
    @location(1) color: vec4<f32>,
}

struct ScreenUniforms {
    screen_size: vec2<f32>,
    aspect_ratio: f32,
    _padding1: f32,
    letterbox_scale: vec2<f32>,
    _padding2: vec2<f32>,
}

@group(1) @binding(0)
var<uniform> screen: ScreenUniforms;

@vertex
fn vs_main(
    vertex: VertexInput,
    instance: TextInstance,
) -> VertexOutput {
    var out: VertexOutput;

    // Transform matrix from instance
    let transform = mat4x4<f32>(
        instance.transform_0,
        instance.transform_1,
        instance.transform_2,
        instance.transform_3,
    );

    // Extract position and scale from transform matrix
    let pos_x = instance.transform_0.w;
    let pos_y = instance.transform_1.w;
    let scale_x = instance.transform_0.x;
    let scale_y = instance.transform_1.y;

    // Center the vertex around origin (0,0) like sprite shader
    let centered_pos = vertex.position.xy - 0.5;

    // Apply scale
    let scaled_pos = vec2<f32>(
        centered_pos.x * scale_x,
        centered_pos.y * scale_y
    );

    // Apply position offset
    let world_pos = scaled_pos + vec2<f32>(pos_x, pos_y);

    // Temporary: revert to original coordinate system to test rendering
    let clip_x = (world_pos.x - 0.5) * 2.0 / screen.aspect_ratio;
    let clip_y = (world_pos.y - 0.5) * 2.0;

    let final_pos = vec4<f32>(
        clip_x,
        clip_y,
        vertex.position.z,
        1.0
    );

    out.clip_position = final_pos;
    
    // Map vertex texture coordinates to atlas coordinates
    let atlas_uv = instance.atlas_coords.xy + vertex.tex_coords * instance.atlas_coords.zw;
    out.tex_coords = atlas_uv;
    out.color = instance.color;

    return out;
}

// Fragment shader

@group(0) @binding(0)
var t_font_atlas: texture_2d<f32>;
@group(0) @binding(1)
var s_font_atlas: sampler;

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    let alpha = textureSample(t_font_atlas, s_font_atlas, in.tex_coords).r;
    return vec4<f32>(in.color.rgb, in.color.a * alpha);
}
