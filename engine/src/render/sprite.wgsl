// Vertex shader

struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) tex_coords: vec2<f32>,
}

struct SpriteInstance {
    @location(2) transform_0: vec4<f32>,
    @location(3) transform_1: vec4<f32>,
    @location(4) transform_2: vec4<f32>,
    @location(5) transform_3: vec4<f32>,
    @location(6) tex_coords: vec4<f32>, // x, y, width, height in texture atlas
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) tex_coords: vec2<f32>,
}

struct ScreenUniforms {
    screen_size: vec2<f32>,
    aspect_ratio: f32,
    _padding1: f32,
    letterbox_scale: vec2<f32>,
    _padding2: vec2<f32>,
}

@group(1) @binding(0)
var<uniform> screen: ScreenUniforms;

@vertex
fn vs_main(
    vertex: VertexInput,
    instance: SpriteInstance,
) -> VertexOutput {
    var out: VertexOutput;

    // Extract sprite parameters from instance transform matrix
    // transform_0.w = x position, transform_1.w = y position
    // transform_2.x = texture aspect ratio, transform_2.w = scale
    // transform_3.w = rotation
    let sprite_pos = vec2<f32>(instance.transform_0.w, instance.transform_1.w);
    let texture_aspect = instance.transform_2.x;
    let sprite_scale = instance.transform_2.w;
    let sprite_rotation = instance.transform_3.w;

    // Center the vertex around origin (0,0)
    let centered_pos = vertex.position.xy - 0.5;

    // Apply rotation
    let cos_r = cos(sprite_rotation);
    let sin_r = sin(sprite_rotation);
    let rotated_pos = vec2<f32>(
        centered_pos.x * cos_r - centered_pos.y * sin_r,
        centered_pos.x * sin_r + centered_pos.y * cos_r
    );

    // Apply scale with texture aspect ratio
    let scaled_pos = vec2<f32>(
        rotated_pos.x * sprite_scale * texture_aspect,
        rotated_pos.y * sprite_scale
    );

    // Apply sprite position offset
    let world_pos = scaled_pos + sprite_pos;

    // New coordinate system: (0,0) = top-left, (1,1) = bottom-right
    // Map to clip space with letterboxing
    let clip_x = (world_pos.x * 2.0 - 1.0) * screen.letterbox_scale.x;
    let clip_y = (1.0 - world_pos.y * 2.0) * screen.letterbox_scale.y;

    let final_pos = vec4<f32>(
        clip_x,
        clip_y,
        vertex.position.z,
        1.0
    );

    out.clip_position = final_pos;
    out.tex_coords = vertex.tex_coords;

    return out;
}

// Fragment shader

@group(0) @binding(0)
var t_diffuse: texture_2d<f32>;
@group(0) @binding(1)
var s_diffuse: sampler;

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    return textureSample(t_diffuse, s_diffuse, in.tex_coords);
}
