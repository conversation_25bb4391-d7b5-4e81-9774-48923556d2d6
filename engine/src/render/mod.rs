use crate::{core, EngineError, Result};
use bytemuck::{Pod, Zeroable};
use std::collections::VecDeque;

#[repr(C)]
#[derive(Co<PERSON>, Clone, Debug, Pod, Zeroable)]
pub struct Vertex {
    pub position: [f32; 3],
    pub tex_coords: [f32; 2],
}

impl Vertex {
    const ATTRIBS: [wgpu::VertexAttribute; 2] = wgpu::vertex_attr_array![
        0 => Float32x3,
        1 => Float32x2
    ];

    pub fn desc() -> wgpu::VertexBufferLayout<'static> {
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<Vertex>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Vertex,
            attributes: &Self::ATTRIBS,
        }
    }
}

#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, Pod, Zeroable)]
pub struct SpriteInstance {
    pub transform: [[f32; 4]; 4],
    pub tex_coords: [f32; 4], // x, y, width, height in texture atlas
}

#[repr(C)]
#[derive(Copy, Clone, Debug, Pod, Zeroable)]
pub struct TextInstance {
    pub transform: [[f32; 4]; 4],
    pub atlas_coords: [f32; 4], // x, y, width, height in font atlas (normalized)
    pub color: [f32; 4],
}

impl SpriteInstance {
    const ATTRIBS: [wgpu::VertexAttribute; 5] = wgpu::vertex_attr_array![
        2 => Float32x4,
        3 => Float32x4,
        4 => Float32x4,
        5 => Float32x4,
        6 => Float32x4
    ];

    pub fn desc() -> wgpu::VertexBufferLayout<'static> {
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<SpriteInstance>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Instance,
            attributes: &Self::ATTRIBS,
        }
    }
}

impl TextInstance {
    const ATTRIBS: [wgpu::VertexAttribute; 6] = wgpu::vertex_attr_array![
        2 => Float32x4,
        3 => Float32x4,
        4 => Float32x4,
        5 => Float32x4,
        6 => Float32x4,
        7 => Float32x4
    ];

    pub fn desc() -> wgpu::VertexBufferLayout<'static> {
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<TextInstance>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Instance,
            attributes: &Self::ATTRIBS,
        }
    }
}

pub struct DrawCommand {
    pub sprite_name: String,
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub scale: f32,
    pub rotation: f32,
}

pub struct TextCommand {
    pub text: String,
    pub font_name: String,
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub scale: f32,
    pub rotation: f32,
}

pub struct Renderer {
    pub render_pipeline: Option<wgpu::RenderPipeline>,
    pub text_render_pipeline: Option<wgpu::RenderPipeline>,
    pub vertex_buffer: Option<wgpu::Buffer>,
    pub index_buffer: Option<wgpu::Buffer>,
    pub instance_buffer: Option<wgpu::Buffer>,
    pub text_instance_buffer: Option<wgpu::Buffer>,
    pub bind_group_layout: Option<wgpu::BindGroupLayout>,
    pub screen_bind_group_layout: Option<wgpu::BindGroupLayout>,
    pub screen_uniform_buffer: Option<wgpu::Buffer>,
    pub screen_bind_group: Option<wgpu::BindGroup>,
    pub sprite_bind_group_layout: Option<wgpu::BindGroupLayout>,
    pub sprite_uniform_buffer: Option<wgpu::Buffer>,
    pub sprite_bind_group: Option<wgpu::BindGroup>,
    pub texture_bind_groups: std::collections::HashMap<String, wgpu::BindGroup>,
    pub font_bind_groups: std::collections::HashMap<String, wgpu::BindGroup>,
    pub draw_commands: VecDeque<DrawCommand>,
    pub text_commands: VecDeque<TextCommand>,
    pub frame_started: bool,
    // MSAA support
    pub msaa_texture: Option<wgpu::Texture>,
    pub msaa_view: Option<wgpu::TextureView>,
    pub current_sample_count: u32,
}

impl Renderer {
    pub fn new(device: &wgpu::Device, queue: &wgpu::Queue) -> Result<Self> {
        // Create quad vertices (normalized coordinates)
        // Fix upside-down texture by flipping Y texture coordinates
        let vertices = [
            Vertex { position: [0.0, 0.0, 0.0], tex_coords: [0.0, 1.0] }, // top-left
            Vertex { position: [1.0, 0.0, 0.0], tex_coords: [1.0, 1.0] }, // top-right
            Vertex { position: [1.0, 1.0, 0.0], tex_coords: [1.0, 0.0] }, // bottom-right
            Vertex { position: [0.0, 1.0, 0.0], tex_coords: [0.0, 0.0] }, // bottom-left
        ];

        let indices: [u16; 6] = [0, 1, 2, 2, 3, 0];

        let vertex_buffer = device.create_buffer(&wgpu::BufferDescriptor {
            label: Some("Vertex Buffer"),
            size: std::mem::size_of_val(&vertices) as u64,
            usage: wgpu::BufferUsages::VERTEX | wgpu::BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        let index_buffer = device.create_buffer(&wgpu::BufferDescriptor {
            label: Some("Index Buffer"),
            size: std::mem::size_of_val(&indices) as u64,
            usage: wgpu::BufferUsages::INDEX | wgpu::BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Upload vertex and index data
        queue.write_buffer(&vertex_buffer, 0, bytemuck::cast_slice(&vertices));
        queue.write_buffer(&index_buffer, 0, bytemuck::cast_slice(&indices));

        // Create instance buffer (will be updated each frame)
        let instance_buffer = device.create_buffer(&wgpu::BufferDescriptor {
            label: Some("Instance Buffer"),
            size: std::mem::size_of::<SpriteInstance>() as u64 * 1000, // Support up to 1000 sprites per frame
            usage: wgpu::BufferUsages::VERTEX | wgpu::BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create text instance buffer
        let text_instance_buffer = device.create_buffer(&wgpu::BufferDescriptor {
            label: Some("Text Instance Buffer"),
            size: std::mem::size_of::<TextInstance>() as u64 * 1000, // Support up to 1000 text instances
            usage: wgpu::BufferUsages::VERTEX | wgpu::BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        Ok(Self {
            render_pipeline: None,
            text_render_pipeline: None,
            vertex_buffer: Some(vertex_buffer),
            index_buffer: Some(index_buffer),
            instance_buffer: Some(instance_buffer),
            text_instance_buffer: Some(text_instance_buffer),
            bind_group_layout: None,
            screen_bind_group_layout: None,
            screen_uniform_buffer: None,
            screen_bind_group: None,
            sprite_bind_group_layout: None,
            sprite_uniform_buffer: None,
            sprite_bind_group: None,
            texture_bind_groups: std::collections::HashMap::new(),
            font_bind_groups: std::collections::HashMap::new(),
            draw_commands: VecDeque::new(),
            text_commands: VecDeque::new(),
            frame_started: false,
            msaa_texture: None,
            msaa_view: None,
            current_sample_count: 1,
        })
    }

    pub fn create_pipeline(&mut self, device: &wgpu::Device, surface_format: wgpu::TextureFormat, sample_count: u32) -> Result<()> {
        // Create bind group layout for textures
        let bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            entries: &[
                wgpu::BindGroupLayoutEntry {
                    binding: 0,
                    visibility: wgpu::ShaderStages::FRAGMENT,
                    ty: wgpu::BindingType::Texture {
                        multisampled: false,
                        view_dimension: wgpu::TextureViewDimension::D2,
                        sample_type: wgpu::TextureSampleType::Float { filterable: true },
                    },
                    count: None,
                },
                wgpu::BindGroupLayoutEntry {
                    binding: 1,
                    visibility: wgpu::ShaderStages::FRAGMENT,
                    ty: wgpu::BindingType::Sampler(wgpu::SamplerBindingType::Filtering),
                    count: None,
                },
            ],
            label: Some("texture_bind_group_layout"),
        });

        // Create bind group layout for screen uniforms
        let screen_bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            entries: &[
                wgpu::BindGroupLayoutEntry {
                    binding: 0,
                    visibility: wgpu::ShaderStages::VERTEX,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
            ],
            label: Some("screen_bind_group_layout"),
        });

        // Create bind group layout for sprite uniforms
        let sprite_bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            entries: &[
                wgpu::BindGroupLayoutEntry {
                    binding: 0,
                    visibility: wgpu::ShaderStages::VERTEX,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
            ],
            label: Some("sprite_bind_group_layout"),
        });

        let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label: Some("Sprite Shader"),
            source: wgpu::ShaderSource::Wgsl(include_str!("sprite.wgsl").into()),
        });

        let render_pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("Render Pipeline Layout"),
            bind_group_layouts: &[&bind_group_layout, &screen_bind_group_layout], // Remove sprite bind group layout
            push_constant_ranges: &[],
        });

        let render_pipeline = device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
            label: Some("Render Pipeline"),
            layout: Some(&render_pipeline_layout),
            vertex: wgpu::VertexState {
                module: &shader,
                entry_point: "vs_main",
                buffers: &[Vertex::desc(), SpriteInstance::desc()], // Vertex buffer + instance buffer
            },
            fragment: Some(wgpu::FragmentState {
                module: &shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::ALPHA_BLENDING),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: Some(wgpu::Face::Back),
                polygon_mode: wgpu::PolygonMode::Fill,
                unclipped_depth: false,
                conservative: false,
            },
            depth_stencil: None, // No depth buffer for 2D rendering
            multisample: wgpu::MultisampleState {
                count: sample_count,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
        });

        // Create screen uniform buffer
        let screen_uniform_buffer = device.create_buffer(&wgpu::BufferDescriptor {
            label: Some("Screen Uniform Buffer"),
            size: 32, // vec2<f32> + f32 + vec2<f32> + f32 padding = 32 bytes (aligned to 16-byte boundary)
            usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create screen bind group
        let screen_bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
            layout: &screen_bind_group_layout,
            entries: &[
                wgpu::BindGroupEntry {
                    binding: 0,
                    resource: screen_uniform_buffer.as_entire_binding(),
                },
            ],
            label: Some("screen_bind_group"),
        });

        // Sprite uniforms are now handled via instancing, no need for uniform buffer

        self.render_pipeline = Some(render_pipeline);
        self.bind_group_layout = Some(bind_group_layout);
        self.screen_bind_group_layout = Some(screen_bind_group_layout);
        self.screen_uniform_buffer = Some(screen_uniform_buffer);
        self.screen_bind_group = Some(screen_bind_group);
        self.sprite_bind_group_layout = Some(sprite_bind_group_layout);
        // Sprite uniforms now handled via instancing
        self.sprite_uniform_buffer = None;
        self.sprite_bind_group = None;

        Ok(())
    }

    pub fn create_text_pipeline(&mut self, device: &wgpu::Device, surface_format: wgpu::TextureFormat, sample_count: u32) -> Result<()> {
        // Reuse the same bind group layouts as sprites for consistency
        let bind_group_layout = self.bind_group_layout.as_ref()
            .ok_or_else(|| EngineError::Render("Bind group layout not initialized".to_string()))?;
        let screen_bind_group_layout = self.screen_bind_group_layout.as_ref()
            .ok_or_else(|| EngineError::Render("Screen bind group layout not initialized".to_string()))?;

        let text_shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label: Some("Text Shader"),
            source: wgpu::ShaderSource::Wgsl(include_str!("text.wgsl").into()),
        });

        let text_render_pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("Text Render Pipeline Layout"),
            bind_group_layouts: &[
                bind_group_layout,
                screen_bind_group_layout,
            ],
            push_constant_ranges: &[],
        });

        let text_render_pipeline = device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
            label: Some("Text Render Pipeline"),
            layout: Some(&text_render_pipeline_layout),
            vertex: wgpu::VertexState {
                module: &text_shader,
                entry_point: "vs_main",
                buffers: &[Vertex::desc(), TextInstance::desc()],
            },
            fragment: Some(wgpu::FragmentState {
                module: &text_shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::ALPHA_BLENDING),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: Some(wgpu::Face::Back),
                polygon_mode: wgpu::PolygonMode::Fill,
                unclipped_depth: false,
                conservative: false,
            },
            depth_stencil: None,
            multisample: wgpu::MultisampleState {
                count: sample_count,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
        });

        self.text_render_pipeline = Some(text_render_pipeline);
        Ok(())
    }

    pub fn create_msaa_texture(&mut self, device: &wgpu::Device, width: u32, height: u32, sample_count: u32, format: wgpu::TextureFormat) -> Result<u32> {
        if sample_count <= 1 {
            // No MSAA needed
            self.msaa_texture = None;
            self.msaa_view = None;
            self.current_sample_count = 1;
            return Ok(1);
        }

        // Find the highest supported sample count that doesn't exceed the requested count
        let supported_sample_count = self.find_supported_sample_count(device, format, sample_count);

        if supported_sample_count <= 1 {
            // No MSAA supported, fall back to no MSAA
            log::warn!("MSAA not supported for format {:?}, falling back to no MSAA", format);
            self.msaa_texture = None;
            self.msaa_view = None;
            self.current_sample_count = 1;
            return Ok(1);
        }

        if supported_sample_count != sample_count {
            log::warn!("Requested MSAA {}x not supported for format {:?}, using {}x instead",
                      sample_count, format, supported_sample_count);
        }

        // Create MSAA texture
        let msaa_texture = device.create_texture(&wgpu::TextureDescriptor {
            label: Some("MSAA Texture"),
            size: wgpu::Extent3d {
                width,
                height,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: supported_sample_count,
            dimension: wgpu::TextureDimension::D2,
            format,
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            view_formats: &[],
        });

        let msaa_view = msaa_texture.create_view(&wgpu::TextureViewDescriptor::default());

        self.msaa_texture = Some(msaa_texture);
        self.msaa_view = Some(msaa_view);
        self.current_sample_count = supported_sample_count;

        log::info!("Created MSAA texture with {}x samples", supported_sample_count);
        Ok(supported_sample_count)
    }

    fn find_supported_sample_count(&self, _device: &wgpu::Device, _format: wgpu::TextureFormat, requested_count: u32) -> u32 {
        // WGPU spec guarantees that 1x and 4x MSAA are supported for all render attachment formats
        // 2x and 8x support varies by device and format, so we'll be conservative

        match requested_count {
            1 => 1,           // No MSAA
            2 => 1,           // 2x not reliably supported, fall back to 1x
            3..=4 => 4,       // Use 4x MSAA
            5..=8 => 4,       // 8x not reliably supported, fall back to 4x
            _ => 4,           // For any higher value, use 4x
        }
    }

    pub fn add_sprite_command(&mut self, sprite_name: String, x: f32, y: f32, z: f32, scale: f32, rotation: f32) {
        self.draw_commands.push_back(DrawCommand {
            sprite_name,
            x,
            y,
            z,
            scale,
            rotation,
        });
    }

    pub fn add_text_command(&mut self, text: String, font_name: String, x: f32, y: f32, z: f32, scale: f32, rotation: f32) {
        self.text_commands.push_back(TextCommand {
            text,
            font_name,
            x,
            y,
            z,
            scale,
            rotation,
        });
    }

    pub fn clear_commands(&mut self) {
        self.draw_commands.clear();
        self.text_commands.clear();
    }

    pub fn update_screen_uniforms(&self, queue: &wgpu::Queue, width: u32, height: u32) -> Result<()> {
        if let Some(ref buffer) = self.screen_uniform_buffer {
            let aspect_ratio = width as f32 / height as f32;
            let target_aspect = 4.0 / 3.0; // 4:3 aspect ratio

            // Calculate letterboxing scale factors
            let (letterbox_scale_x, letterbox_scale_y) = if aspect_ratio > target_aspect {
                // Window is too wide - add pillarboxing (vertical black bars)
                (target_aspect / aspect_ratio, 1.0)
            } else {
                // Window is too tall - add letterboxing (horizontal black bars)
                (1.0, aspect_ratio / target_aspect)
            };

            let screen_data = [
                width as f32,        // screen_size.x
                height as f32,       // screen_size.y
                aspect_ratio,        // aspect_ratio
                0.0,                // padding after aspect_ratio
                letterbox_scale_x,   // letterbox_scale.x
                letterbox_scale_y,   // letterbox_scale.y
                0.0,                // padding
                0.0,                // additional padding to reach 32 bytes
            ];

            queue.write_buffer(buffer, 0, bytemuck::cast_slice(&screen_data));
            Ok(())
        } else {
            Err(EngineError::Render("Screen uniform buffer not initialized".to_string()))
        }
    }

    // Sprite uniforms are now handled via instancing, no longer needed

    pub fn create_texture_bind_group(&mut self, device: &wgpu::Device, texture: &wgpu::Texture, name: &str) -> Result<()> {
        if let Some(ref bind_group_layout) = self.bind_group_layout {
            // Create texture view
            let texture_view = texture.create_view(&wgpu::TextureViewDescriptor::default());

            // Create sampler with nearest neighbor filtering for crisp pixels
            let sampler = device.create_sampler(&wgpu::SamplerDescriptor {
                address_mode_u: wgpu::AddressMode::ClampToEdge,
                address_mode_v: wgpu::AddressMode::ClampToEdge,
                address_mode_w: wgpu::AddressMode::ClampToEdge,
                mag_filter: wgpu::FilterMode::Nearest, // Changed from Linear to Nearest
                min_filter: wgpu::FilterMode::Nearest,
                mipmap_filter: wgpu::FilterMode::Nearest,
                ..Default::default()
            });

            // Create bind group
            let bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
                layout: bind_group_layout,
                entries: &[
                    wgpu::BindGroupEntry {
                        binding: 0,
                        resource: wgpu::BindingResource::TextureView(&texture_view),
                    },
                    wgpu::BindGroupEntry {
                        binding: 1,
                        resource: wgpu::BindingResource::Sampler(&sampler),
                    },
                ],
                label: Some(&format!("Texture Bind Group: {}", name)),
            });

            self.texture_bind_groups.insert(name.to_string(), bind_group);
            Ok(())
        } else {
            Err(EngineError::Render("Bind group layout not initialized".to_string()))
        }
    }

    pub fn create_font_bind_group(&mut self, device: &wgpu::Device, font_atlas_view: &wgpu::TextureView, name: &str) -> Result<()> {
        if let Some(ref bind_group_layout) = self.bind_group_layout {
            // Create sampler with nearest filtering for pixelated text
            let sampler = device.create_sampler(&wgpu::SamplerDescriptor {
                address_mode_u: wgpu::AddressMode::ClampToEdge,
                address_mode_v: wgpu::AddressMode::ClampToEdge,
                address_mode_w: wgpu::AddressMode::ClampToEdge,
                mag_filter: wgpu::FilterMode::Nearest,
                min_filter: wgpu::FilterMode::Nearest,
                mipmap_filter: wgpu::FilterMode::Nearest,
                ..Default::default()
            });

            // Create bind group
            let bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
                layout: bind_group_layout,
                entries: &[
                    wgpu::BindGroupEntry {
                        binding: 0,
                        resource: wgpu::BindingResource::TextureView(font_atlas_view),
                    },
                    wgpu::BindGroupEntry {
                        binding: 1,
                        resource: wgpu::BindingResource::Sampler(&sampler),
                    },
                ],
                label: Some(&format!("Font Bind Group: {}", name)),
            });

            self.font_bind_groups.insert(name.to_string(), bind_group);
            Ok(())
        } else {
            Err(EngineError::Render("Bind group layout not initialized".to_string()))
        }
    }
}

/// Begin frame rendering
pub fn start() -> Result<()> {
    core::with_engine_state(|state| {
        if state.renderer.frame_started {
            return Err(EngineError::Render("Frame already started".to_string()));
        }

        state.renderer.frame_started = true;
        state.renderer.clear_commands();

        // Update delta time and FPS
        let now = std::time::Instant::now();
        state.delta_time = now.duration_since(state.last_frame_time).as_secs_f32();
        state.last_frame_time = now;

        state.frame_count += 1;
        if now.duration_since(state.fps_timer).as_secs_f32() >= 1.0 {
            state.fps = state.frame_count as f32;
            state.frame_count = 0;
            state.fps_timer = now;
        }

        Ok(())
    })
}

/// End frame and present
pub fn stop() -> Result<()> {
    core::with_engine_state(|state| {
        if !state.renderer.frame_started {
            return Err(EngineError::Render("Frame not started".to_string()));
        }

        // Get surface and render
        if let (Some(surface), Some(config)) = (&state.surface, &state.surface_config) {
            // Update screen uniforms with current dimensions
            state.renderer.update_screen_uniforms(&state.queue, config.width, config.height)?;

            let output = surface.get_current_texture()
                .map_err(|e| EngineError::Render(format!("Failed to get surface texture: {}", e)))?;

            let view = output.texture.create_view(&wgpu::TextureViewDescriptor::default());

            let mut encoder = state.device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Render Encoder"),
            });

            // Render pass
            {
                // Determine render target and resolve target based on MSAA
                let (render_view, resolve_target) = if state.renderer.current_sample_count > 1 {
                    // Use MSAA texture as render target, surface as resolve target
                    if let Some(ref msaa_view) = state.renderer.msaa_view {
                        (msaa_view, Some(&view))
                    } else {
                        // Fallback to direct rendering if MSAA texture not available
                        (&view, None)
                    }
                } else {
                    // Direct rendering to surface
                    (&view, None)
                };

                let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                    label: Some("Render Pass"),
                    color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                        view: render_view,
                        resolve_target,
                        ops: wgpu::Operations {
                            load: wgpu::LoadOp::Clear(wgpu::Color {
                                r: 0.1,
                                g: 0.2,
                                b: 0.3,
                                a: 1.0,
                            }),
                            store: wgpu::StoreOp::Store,
                        },
                    })],
                    depth_stencil_attachment: None,
                    occlusion_query_set: None,
                    timestamp_writes: None,
                });

                // Render sprites if we have a pipeline
                if let Some(ref pipeline) = state.renderer.render_pipeline {
                    render_pass.set_pipeline(pipeline);

                    // Bind screen uniforms (bind group 1)
                    if let Some(ref screen_bind_group) = state.renderer.screen_bind_group {
                        render_pass.set_bind_group(1, screen_bind_group, &[]);
                    }

                    // Set vertex and index buffers
                    if let (Some(ref vertex_buffer), Some(ref index_buffer)) =
                        (&state.renderer.vertex_buffer, &state.renderer.index_buffer) {
                        render_pass.set_vertex_buffer(0, vertex_buffer.slice(..));
                        render_pass.set_index_buffer(index_buffer.slice(..), wgpu::IndexFormat::Uint16);

                        // Set the sprite render pipeline
                        if let Some(ref pipeline) = state.renderer.render_pipeline {
                            render_pass.set_pipeline(pipeline);

                            // Set vertex and index buffers once
                            if let (Some(ref vertex_buffer), Some(ref index_buffer), Some(ref instance_buffer)) =
                                (&state.renderer.vertex_buffer, &state.renderer.index_buffer, &state.renderer.instance_buffer) {

                                render_pass.set_vertex_buffer(0, vertex_buffer.slice(..));
                                render_pass.set_vertex_buffer(1, instance_buffer.slice(..));
                                render_pass.set_index_buffer(index_buffer.slice(..), wgpu::IndexFormat::Uint16);

                                // Collect all sprite instances first
                                let mut all_instances = Vec::new();
                                let mut sprite_ranges = Vec::new(); // (sprite_name, start_index, count)

                                for command in &state.renderer.draw_commands {
                                    if state.renderer.texture_bind_groups.contains_key(&command.sprite_name) {
                                        let start_index = all_instances.len();

                                        // Get texture aspect ratio
                                        let texture_aspect = if let Some(texture) = state.asset_manager.sprites.get(&command.sprite_name) {
                                            texture.width as f32 / texture.height as f32
                                        } else {
                                            1.0 // Default to square if texture not found
                                        };

                                        // Create sprite instance data
                                        let instance = SpriteInstance {
                                            transform: [
                                                [0.0, 0.0, 0.0, command.x],           // x position in w component
                                                [0.0, 0.0, 0.0, command.y],           // y position in w component
                                                [texture_aspect, 0.0, 0.0, command.scale], // texture aspect in x, scale in w component
                                                [0.0, 0.0, 0.0, command.rotation],    // rotation in w component
                                            ],
                                            tex_coords: [0.0, 0.0, 1.0, 1.0], // Full texture for now
                                        };

                                        all_instances.push(instance);
                                        sprite_ranges.push((command.sprite_name.clone(), start_index, 1));
                                    }
                                }

                                // Write all instances to buffer at once
                                if !all_instances.is_empty() {
                                    state.queue.write_buffer(
                                        instance_buffer,
                                        0,
                                        bytemuck::cast_slice(&all_instances),
                                    );

                                    // Render each sprite with its range in the instance buffer
                                    for (sprite_name, start_index, count) in sprite_ranges {
                                        if let Some(bind_group) = state.renderer.texture_bind_groups.get(&sprite_name) {
                                            render_pass.set_bind_group(0, bind_group, &[]);
                                            if let Some(ref screen_bind_group) = state.renderer.screen_bind_group {
                                                render_pass.set_bind_group(1, screen_bind_group, &[]);
                                            }

                                            render_pass.draw_indexed(0..6, 0, start_index as u32..(start_index + count) as u32);
                                        }
                                    }
                                }
                            }
                        }

                        // Render text commands
                        if !state.renderer.text_commands.is_empty() {
                            if let (Some(ref text_pipeline), Some(ref vertex_buffer), Some(ref index_buffer), Some(ref text_instance_buffer)) =
                                (&state.renderer.text_render_pipeline, &state.renderer.vertex_buffer, &state.renderer.index_buffer, &state.renderer.text_instance_buffer) {

                                // Generate text instances
                                let mut text_instances = Vec::new();
                                for text_command in &state.renderer.text_commands {
                                    if let Some(font) = state.asset_manager.fonts.get(&text_command.font_name) {
                                        if let Some(ref atlas) = font.atlas {
                                            let instances = generate_text_instances(text_command, &font.font, atlas);
                                            text_instances.extend(instances);
                                        }
                                    }
                                }

                                if !text_instances.is_empty() {
                                    // Update text instance buffer
                                    state.queue.write_buffer(
                                        text_instance_buffer,
                                        0,
                                        bytemuck::cast_slice(&text_instances),
                                    );

                                    render_pass.set_pipeline(text_pipeline);
                                    render_pass.set_vertex_buffer(0, vertex_buffer.slice(..));
                                    render_pass.set_vertex_buffer(1, text_instance_buffer.slice(..));
                                    render_pass.set_index_buffer(index_buffer.slice(..), wgpu::IndexFormat::Uint16);

                                    // Render all text instances with the first font (simplified for now)
                                    if let Some(first_command) = state.renderer.text_commands.front() {
                                        if let Some(bind_group) = state.renderer.font_bind_groups.get(&first_command.font_name) {
                                            render_pass.set_bind_group(0, bind_group, &[]);
                                            if let Some(ref screen_bind_group) = state.renderer.screen_bind_group {
                                                render_pass.set_bind_group(1, screen_bind_group, &[]);
                                            }
                                            let instance_count = text_instances.len() as u32;
                                            render_pass.draw_indexed(0..6, 0, 0..instance_count);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            state.queue.submit(std::iter::once(encoder.finish()));
            output.present();
        }

        state.renderer.frame_started = false;
        Ok(())
    })
}

/// Clear depth buffer
pub fn cleardepth() -> Result<()> {
    // Depth clearing will be handled in the render pass
    Ok(())
}

/// Clear color buffer
pub fn clearcolor() -> Result<()> {
    // Color clearing will be handled in the render pass
    Ok(())
}

/// Draw sprite at normalized position with scale and rotation
pub fn sprite(sprite_name: &str, x: f32, y: f32, z: f32, scale: f32, rotation: f32) -> Result<()> {
    core::with_engine_state(|state| {
        if !state.renderer.frame_started {
            return Err(EngineError::Render("Frame not started. Call start() first.".to_string()));
        }

        state.renderer.add_sprite_command(sprite_name.to_string(), x, y, z, scale, rotation);
        Ok(())
    })
}

/// Draw text at normalized position with scale and rotation
pub fn text(text: &str, font_name: &str, x: f32, y: f32, z: f32, scale: f32, rotation: f32) -> Result<()> {
    core::with_engine_state(|state| {
        if !state.renderer.frame_started {
            return Err(EngineError::Render("Frame not started. Call start() first.".to_string()));
        }

        state.renderer.add_text_command(text.to_string(), font_name.to_string(), x, y, z, scale, rotation);
        Ok(())
    })
}

/// Get frame delta time in seconds
pub fn get_delta_time() -> Result<f32> {
    core::with_engine_state(|state| Ok(state.delta_time))
}

/// Get current FPS
pub fn get_fps() -> Result<f32> {
    core::with_engine_state(|state| Ok(state.fps))
}

/// Generate text instances for rendering individual glyphs
fn generate_text_instances(text_command: &TextCommand, font: &fontdue::Font, atlas: &crate::asset::TextureAtlas) -> Vec<TextInstance> {
    let mut instances = Vec::new();
    let mut cursor_x = text_command.x;
    let cursor_y = text_command.y;

    // Font size for metrics calculation
    const FONT_SIZE: f32 = 48.0; // Same as atlas generation
    let scale_factor = text_command.scale * 0.01; // Scale down for screen coordinates

    for ch in text_command.text.chars() {
        // Get font metrics for this character
        let (metrics, _) = font.rasterize(ch, FONT_SIZE);

        // Use advance width from font metrics for proper spacing
        let advance_width = metrics.advance_width * scale_factor;

        if let Some(entry) = atlas.entries.get(&ch) {
            if entry.width > 0 && entry.height > 0 {
                // Calculate glyph position and size using font metrics
                let glyph_width = entry.width as f32 * scale_factor;
                let glyph_height = entry.height as f32 * scale_factor;

                // Apply proper baseline positioning using font metrics
                let pos_x = cursor_x + (metrics.xmin as f32 * scale_factor);
                let pos_y = cursor_y + (metrics.ymin as f32 * scale_factor);

                // Create simple transform matrix (scale and translate)
                // Keep Z at 0.0 for all characters to avoid perspective distortion
                let transform = [
                    [glyph_width, 0.0, 0.0, pos_x],
                    [0.0, glyph_height, 0.0, pos_y],
                    [0.0, 0.0, 1.0, 0.0],
                    [0.0, 0.0, 0.0, 1.0],
                ];

                // Calculate normalized atlas coordinates
                let atlas_x = entry.x as f32 / atlas.width as f32;
                let atlas_y = entry.y as f32 / atlas.height as f32;
                let atlas_w = entry.width as f32 / atlas.width as f32;
                let atlas_h = entry.height as f32 / atlas.height as f32;

                instances.push(TextInstance {
                    transform,
                    atlas_coords: [atlas_x, atlas_y, atlas_w, atlas_h],
                    color: [1.0, 1.0, 1.0, 1.0], // White text
                });
            }
        }

        // Advance cursor using proper font advance width
        cursor_x += advance_width;
    }

    instances
}
